#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ClassyFire化学品分类完整工具
包括提交、监控、查询和保存功能

功能:
1. 批量提交SMILES到ClassyFire
2. 监控分类进度
3. 查询和获取结果
4. 保存到Excel文件
5. 支持断点续传和错误恢复
"""

import pandas as pd
import requests
import time
import json
import os
from typing import Dict, Optional, List, Tuple
import logging
from datetime import datetime
import pickle

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classyfire.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ClassyFireProcessor:
    """
    ClassyFire化学品分类处理器
    支持批量提交、监控和结果获取
    """
    
    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 状态文件，用于断点续传
        self.status_file = "classyfire_status.pkl"
        self.results_file = "classyfire_results.pkl"
        
        # API限制参数
        self.max_batch_size = 100  # 每批最大化合物数量
        self.request_delay = 2.0   # 请求间隔（秒）
        self.max_retries = 3       # 最大重试次数
        self.timeout = 30          # 请求超时时间
        
    def validate_smiles(self, smiles: str) -> Tuple[bool, str]:
        """
        验证SMILES字符串
        
        Args:
            smiles: SMILES字符串
            
        Returns:
            (是否有效, 错误信息)
        """
        if pd.isna(smiles) or not smiles.strip():
            return False, "空SMILES"
            
        smiles = smiles.strip()
        
        # 检查长度
        if len(smiles) > 2000:
            return False, f"SMILES过长({len(smiles)}字符)"
            
        # 检查氟原子数量
        fluorine_count = smiles.count('F')
        if fluorine_count > 100:
            return False, f"氟原子过多({fluorine_count}个)"
            
        # 检查基本格式
        if any(char in smiles for char in ['<', '>', '|', '\\', '/']):
            if smiles.count('/') + smiles.count('\\') > len(smiles) * 0.3:
                return False, "立体化学信息过多"
                
        return True, ""
    
    def submit_compound(self, smiles: str, compound_id: str = None) -> Optional[str]:
        """
        提交单个化合物到ClassyFire
        
        Args:
            smiles: SMILES字符串
            compound_id: 化合物ID（可选）
            
        Returns:
            查询ID，失败返回None
        """
        # 验证SMILES
        is_valid, error_msg = self.validate_smiles(smiles)
        if not is_valid:
            logger.warning(f"跳过无效SMILES: {error_msg} - {smiles[:100]}")
            return None
            
        for attempt in range(self.max_retries):
            try:
                submit_url = f"{self.base_url}/entities.json"
                data = {
                    'label': compound_id or 'compound',
                    'smiles': smiles,
                    'structure_source': 'SMILES'
                }
                
                response = self.session.post(submit_url, data=data, timeout=self.timeout)
                
                if response.status_code == 201:
                    result = response.json()
                    query_id = result.get('id')
                    if query_id:
                        logger.info(f"成功提交化合物 {compound_id}: {query_id}")
                        return str(query_id)
                        
                elif response.status_code == 422:
                    logger.error(f"SMILES格式错误: {smiles[:100]}")
                    return None
                    
                elif response.status_code == 429:
                    logger.warning("API请求频率限制，等待更长时间...")
                    time.sleep(self.request_delay * 10)
                    
                else:
                    logger.warning(f"提交失败，状态码: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"提交化合物时出错 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                
            if attempt < self.max_retries - 1:
                time.sleep(self.request_delay * (attempt + 1))
                
        return None
    
    def check_classification_status(self, query_id: str) -> Tuple[str, Optional[Dict]]:
        """
        检查分类状态
        
        Args:
            query_id: 查询ID
            
        Returns:
            (状态, 结果数据) - 状态: 'pending', 'completed', 'failed'
        """
        try:
            result_url = f"{self.base_url}/entities/{query_id}.json"
            response = self.session.get(result_url, timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查是否有分类信息
                if any(key in result for key in ['kingdom', 'superclass', 'class', 'subclass']):
                    classification = self.extract_classification_info(result)
                    return 'completed', classification
                else:
                    return 'pending', None
                    
            elif response.status_code == 404:
                return 'failed', None
            else:
                logger.warning(f"查询状态失败，状态码: {response.status_code}")
                return 'pending', None
                
        except Exception as e:
            logger.error(f"查询状态时出错: {str(e)}")
            return 'pending', None
    
    def extract_classification_info(self, result: Dict) -> Dict:
        """
        提取分类信息
        
        Args:
            result: ClassyFire返回的结果
            
        Returns:
            分类信息字典
        """
        classification = {}
        
        # 提取各级分类信息
        levels = ['kingdom', 'superclass', 'class', 'subclass', 'level_5', 'level_6', 'level_7', 'level_8']
        
        for level in levels:
            if level in result and result[level]:
                classification[f'{level}_name'] = result[level].get('name', '')
                classification[f'{level}_chemont_id'] = result[level].get('chemont_id', '')
                classification[f'{level}_description'] = result[level].get('description', '')
        
        # 提取其他信息
        if 'smiles' in result:
            classification['canonical_smiles'] = result['smiles']
        if 'inchikey' in result:
            classification['inchikey'] = result['inchikey']
        if 'molecular_formula' in result:
            classification['molecular_formula'] = result['molecular_formula']
            
        # 提取直接父类信息
        if 'direct_parent' in result and result['direct_parent']:
            classification['direct_parent_name'] = result['direct_parent'].get('name', '')
            classification['direct_parent_chemont_id'] = result['direct_parent'].get('chemont_id', '')
            
        # 提取替代父类信息
        if 'alternative_parents' in result and result['alternative_parents']:
            alt_parents = []
            for parent in result['alternative_parents'][:3]:  # 只取前3个
                alt_parents.append(parent.get('name', ''))
            classification['alternative_parents'] = '; '.join(alt_parents)
            
        return classification
    
    def save_status(self, status_data: Dict) -> None:
        """保存状态到文件"""
        try:
            with open(self.status_file, 'wb') as f:
                pickle.dump(status_data, f)
        except Exception as e:
            logger.error(f"保存状态失败: {str(e)}")
    
    def load_status(self) -> Dict:
        """从文件加载状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.error(f"加载状态失败: {str(e)}")
        return {}
    
    def save_results(self, results: Dict) -> None:
        """保存结果到文件"""
        try:
            with open(self.results_file, 'wb') as f:
                pickle.dump(results, f)
        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")
    
    def load_results(self) -> Dict:
        """从文件加载结果"""
        try:
            if os.path.exists(self.results_file):
                with open(self.results_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.error(f"加载结果失败: {str(e)}")
        return {}

    def submit_batch(self, df: pd.DataFrame, smiles_column: str = 'smiles',
                    id_column: str = None, start_idx: int = 0) -> Dict:
        """
        批量提交化合物

        Args:
            df: 包含SMILES的DataFrame
            smiles_column: SMILES列名
            id_column: ID列名（可选）
            start_idx: 开始索引

        Returns:
            提交状态字典 {index: query_id}
        """
        logger.info(f"开始批量提交，从索引 {start_idx} 开始，共 {len(df)} 个化合物")

        # 加载已有状态
        status_data = self.load_status()
        submitted = status_data.get('submitted', {})

        total_submitted = 0

        for idx, row in df.iterrows():
            if idx < start_idx:
                continue

            # 检查是否已提交
            if str(idx) in submitted:
                logger.info(f"跳过已提交的化合物 {idx}")
                continue

            smiles = row[smiles_column]
            compound_id = row[id_column] if id_column and id_column in row else f"compound_{idx}"

            # 提交化合物
            query_id = self.submit_compound(smiles, compound_id)

            if query_id:
                submitted[str(idx)] = {
                    'query_id': query_id,
                    'smiles': smiles,
                    'compound_id': compound_id,
                    'submit_time': datetime.now().isoformat(),
                    'status': 'submitted'
                }
                total_submitted += 1

                # 保存状态
                status_data['submitted'] = submitted
                status_data['last_submit_idx'] = idx
                self.save_status(status_data)

                logger.info(f"已提交 {total_submitted} 个化合物，当前索引: {idx}")
            else:
                logger.warning(f"提交失败，索引: {idx}, SMILES: {smiles[:50]}")

            # 控制请求频率
            time.sleep(self.request_delay)

            # 每100个化合物暂停更长时间
            if total_submitted % 100 == 0 and total_submitted > 0:
                logger.info(f"已提交 {total_submitted} 个化合物，暂停30秒...")
                time.sleep(30)

        logger.info(f"批量提交完成，共提交 {total_submitted} 个化合物")
        return submitted

    def monitor_progress(self, max_wait_hours: int = 24) -> Dict:
        """
        监控分类进度

        Args:
            max_wait_hours: 最大等待时间（小时）

        Returns:
            结果字典
        """
        logger.info("开始监控分类进度...")

        # 加载状态和结果
        status_data = self.load_status()
        submitted = status_data.get('submitted', {})
        results = self.load_results()

        if not submitted:
            logger.warning("没有找到已提交的化合物")
            return results

        start_time = time.time()
        max_wait_seconds = max_wait_hours * 3600
        check_interval = 60  # 每分钟检查一次

        pending_queries = {k: v for k, v in submitted.items()
                          if v.get('status') != 'completed' and str(k) not in results}

        logger.info(f"需要监控 {len(pending_queries)} 个查询")

        while pending_queries and (time.time() - start_time) < max_wait_seconds:
            completed_count = 0

            for idx, query_info in list(pending_queries.items()):
                query_id = query_info['query_id']

                # 检查状态
                status, classification = self.check_classification_status(query_id)

                if status == 'completed':
                    results[idx] = {
                        'query_id': query_id,
                        'compound_id': query_info['compound_id'],
                        'smiles': query_info['smiles'],
                        'classification': classification,
                        'complete_time': datetime.now().isoformat()
                    }

                    # 更新状态
                    submitted[idx]['status'] = 'completed'
                    del pending_queries[idx]
                    completed_count += 1

                    logger.info(f"完成分类: {query_info['compound_id']} ({query_id})")

                elif status == 'failed':
                    logger.error(f"分类失败: {query_info['compound_id']} ({query_id})")
                    submitted[idx]['status'] = 'failed'
                    del pending_queries[idx]

                # 控制查询频率
                time.sleep(1)

            # 保存进度
            if completed_count > 0:
                status_data['submitted'] = submitted
                self.save_status(status_data)
                self.save_results(results)
                logger.info(f"本轮完成 {completed_count} 个分类，剩余 {len(pending_queries)} 个")

            # 等待下次检查
            if pending_queries:
                logger.info(f"等待 {check_interval} 秒后继续检查...")
                time.sleep(check_interval)

        # 最终保存
        self.save_results(results)

        logger.info(f"监控完成，共获得 {len(results)} 个结果")
        return results

    def export_results_to_excel(self, results: Dict, original_df: pd.DataFrame,
                               output_file: str, smiles_column: str = 'smiles') -> None:
        """
        导出结果到Excel文件

        Args:
            results: 分类结果字典
            original_df: 原始DataFrame
            output_file: 输出文件路径
            smiles_column: SMILES列名
        """
        logger.info(f"开始导出结果到 {output_file}")

        # 创建结果DataFrame
        result_df = original_df.copy()

        # 初始化分类列
        classification_columns = [
            'kingdom_name', 'kingdom_chemont_id',
            'superclass_name', 'superclass_chemont_id',
            'class_name', 'class_chemont_id',
            'subclass_name', 'subclass_chemont_id',
            'level_5_name', 'level_5_chemont_id',
            'level_6_name', 'level_6_chemont_id',
            'level_7_name', 'level_7_chemont_id',
            'level_8_name', 'level_8_chemont_id',
            'direct_parent_name', 'direct_parent_chemont_id',
            'alternative_parents',
            'canonical_smiles', 'inchikey', 'molecular_formula',
            'classyfire_query_id', 'classification_status'
        ]

        for col in classification_columns:
            result_df[col] = ''

        # 填充分类结果
        classified_count = 0
        for idx_str, result_data in results.items():
            idx = int(idx_str)
            if idx < len(result_df):
                classification = result_data.get('classification', {})

                # 填充分类信息
                for col in classification_columns[:-2]:  # 除了最后两列
                    if col in classification:
                        result_df.loc[idx, col] = classification[col]

                # 填充查询ID和状态
                result_df.loc[idx, 'classyfire_query_id'] = result_data.get('query_id', '')
                result_df.loc[idx, 'classification_status'] = 'completed'
                classified_count += 1

        # 标记未分类的化合物
        status_data = self.load_status()
        submitted = status_data.get('submitted', {})

        for idx_str, submit_info in submitted.items():
            idx = int(idx_str)
            if idx < len(result_df) and idx_str not in results:
                result_df.loc[idx, 'classyfire_query_id'] = submit_info.get('query_id', '')
                status = submit_info.get('status', 'unknown')
                result_df.loc[idx, 'classification_status'] = status

        # 保存到Excel
        try:
            result_df.to_excel(output_file, index=False)
            logger.info(f"成功导出 {len(result_df)} 行数据到 {output_file}")
            logger.info(f"其中 {classified_count} 个化合物已完成分类")
        except Exception as e:
            logger.error(f"导出Excel失败: {str(e)}")

    def process_excel_file(self, input_file: str, output_file: str,
                          smiles_column: str = 'smiles', id_column: str = None,
                          start_idx: int = 0, max_wait_hours: int = 24) -> None:
        """
        处理Excel文件的完整流程

        Args:
            input_file: 输入Excel文件路径
            output_file: 输出Excel文件路径
            smiles_column: SMILES列名
            id_column: ID列名（可选）
            start_idx: 开始索引
            max_wait_hours: 最大等待时间（小时）
        """
        logger.info(f"开始处理文件: {input_file}")

        # 读取Excel文件
        try:
            df = pd.read_excel(input_file)
            logger.info(f"成功读取文件，共 {len(df)} 行数据")
        except Exception as e:
            logger.error(f"读取文件失败: {str(e)}")
            return

        # 检查必要列
        if smiles_column not in df.columns:
            logger.error(f"未找到SMILES列: {smiles_column}")
            return

        # 第一步：批量提交
        logger.info("=== 第一步：批量提交化合物 ===")
        submitted = self.submit_batch(df, smiles_column, id_column, start_idx)

        if not submitted:
            logger.error("没有成功提交任何化合物")
            return

        # 第二步：监控进度
        logger.info("=== 第二步：监控分类进度 ===")
        results = self.monitor_progress(max_wait_hours)

        # 第三步：导出结果
        logger.info("=== 第三步：导出结果 ===")
        self.export_results_to_excel(results, df, output_file, smiles_column)

        # 生成统计报告
        self.generate_summary_report(submitted, results)

        logger.info("处理完成！")

    def generate_summary_report(self, submitted: Dict, results: Dict) -> None:
        """生成统计报告"""
        total_submitted = len(submitted)
        total_completed = len(results)

        # 统计各状态数量
        status_counts = {}
        for submit_info in submitted.values():
            status = submit_info.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1

        logger.info("=== 处理统计报告 ===")
        logger.info(f"总提交数量: {total_submitted}")
        logger.info(f"完成分类数量: {total_completed}")
        logger.info(f"完成率: {total_completed/total_submitted*100:.1f}%" if total_submitted > 0 else "完成率: 0%")

        for status, count in status_counts.items():
            logger.info(f"{status}: {count}")

        # 保存报告到文件
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_submitted': total_submitted,
            'total_completed': total_completed,
            'completion_rate': total_completed/total_submitted if total_submitted > 0 else 0,
            'status_counts': status_counts
        }

        try:
            with open('classyfire_report.json', 'w') as f:
                json.dump(report, f, indent=2)
            logger.info("统计报告已保存到 classyfire_report.json")
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")


# =============================================================================
# 配置参数 - 请在这里修改你的设置
# =============================================================================

# 输入输出文件配置
INPUT_FILE = 'IECSC_WangHB.xlsx'  # 输入Excel文件路径
OUTPUT_FILE = 'IECSC_WangHB_classified.xlsx'  # 输出Excel文件路径

# 列名配置
SMILES_COLUMN = 'smiles'  # SMILES列名
ID_COLUMN = None  # ID列名（可选，如果有的话）

# 处理参数
START_INDEX = 0  # 开始处理的行索引（用于断点续传）
MAX_WAIT_HOURS = 48  # 最大等待时间（小时）

# =============================================================================
# 工具函数
# =============================================================================

def resume_from_checkpoint():
    """从检查点恢复处理"""
    processor = ClassyFireProcessor()

    # 检查是否有未完成的任务
    status_data = processor.load_status()
    if status_data.get('submitted'):
        logger.info("发现未完成的任务，开始从检查点恢复...")

        # 只进行监控和导出
        results = processor.monitor_progress(MAX_WAIT_HOURS)

        # 读取原始文件进行导出
        try:
            df = pd.read_excel(INPUT_FILE)
            processor.export_results_to_excel(results, df, OUTPUT_FILE, SMILES_COLUMN)
            processor.generate_summary_report(status_data['submitted'], results)
        except Exception as e:
            logger.error(f"恢复处理失败: {str(e)}")
    else:
        logger.info("没有找到未完成的任务")

def clean_checkpoint():
    """清理检查点文件"""
    files_to_clean = ['classyfire_status.pkl', 'classyfire_results.pkl', 'classyfire.log']

    for file_path in files_to_clean:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"已删除文件: {file_path}")
            except Exception as e:
                logger.error(f"删除文件失败 {file_path}: {str(e)}")

def main():
    """主函数"""
    print("ClassyFire化学品分类工具")
    print("=" * 50)

    # 显示配置信息
    print(f"输入文件: {INPUT_FILE}")
    print(f"输出文件: {OUTPUT_FILE}")
    print(f"SMILES列: {SMILES_COLUMN}")
    print(f"开始索引: {START_INDEX}")
    print(f"最大等待时间: {MAX_WAIT_HOURS} 小时")
    print()

    # 检查输入文件
    if not os.path.exists(INPUT_FILE):
        logger.error(f"输入文件不存在: {INPUT_FILE}")
        return

    # 创建处理器
    processor = ClassyFireProcessor()

    # 检查是否需要从检查点恢复
    status_data = processor.load_status()
    if status_data.get('submitted'):
        choice = input("发现未完成的任务，是否从检查点恢复？(y/n): ").lower()
        if choice == 'y':
            resume_from_checkpoint()
            return
        else:
            choice = input("是否清理检查点文件重新开始？(y/n): ").lower()
            if choice == 'y':
                clean_checkpoint()

    try:
        # 开始完整处理流程
        processor.process_excel_file(
            input_file=INPUT_FILE,
            output_file=OUTPUT_FILE,
            smiles_column=SMILES_COLUMN,
            id_column=ID_COLUMN,
            start_idx=START_INDEX,
            max_wait_hours=MAX_WAIT_HOURS
        )

    except KeyboardInterrupt:
        logger.info("用户中断处理，进度已保存到检查点文件")
        logger.info("可以稍后运行程序从检查点恢复")

    except Exception as e:
        logger.error(f"处理过程中出错: {str(e)}")
        logger.info("进度已保存，可以稍后从检查点恢复")

if __name__ == "__main__":
    main()
