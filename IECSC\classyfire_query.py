#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ClassyFire查询工具
专门用于查询已提交的ClassyFire任务状态和结果

使用方法:
1. 单个查询: python classyfire_query.py --query_id YOUR_QUERY_ID
2. 批量查询: python classyfire_query.py --batch_file query_ids.txt
3. 从检查点查询: python classyfire_query.py --from_checkpoint
"""

import requests
import json
import time
import argparse
import logging
from typing import Dict, Optional, List
import pandas as pd

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClassyFireQuery:
    """ClassyFire查询工具"""
    
    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def query_single(self, query_id: str) -> Optional[Dict]:
        """
        查询单个ClassyFire结果
        
        Args:
            query_id: 查询ID
            
        Returns:
            分类结果字典，失败返回None
        """
        try:
            result_url = f"{self.base_url}/entities/{query_id}.json"
            response = self.session.get(result_url, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查是否有分类信息
                if any(key in result for key in ['kingdom', 'superclass', 'class', 'subclass']):
                    classification = self.extract_classification_info(result)
                    return {
                        'query_id': query_id,
                        'status': 'completed',
                        'classification': classification
                    }
                else:
                    return {
                        'query_id': query_id,
                        'status': 'pending',
                        'classification': None
                    }
                    
            elif response.status_code == 404:
                return {
                    'query_id': query_id,
                    'status': 'not_found',
                    'classification': None
                }
            else:
                logger.warning(f"查询失败，状态码: {response.status_code}")
                return {
                    'query_id': query_id,
                    'status': 'error',
                    'classification': None
                }
                
        except Exception as e:
            logger.error(f"查询 {query_id} 时出错: {str(e)}")
            return {
                'query_id': query_id,
                'status': 'error',
                'classification': None
            }
    
    def extract_classification_info(self, result: Dict) -> Dict:
        """提取分类信息"""
        classification = {}
        
        # 提取各级分类信息
        levels = ['kingdom', 'superclass', 'class', 'subclass', 'level_5', 'level_6', 'level_7', 'level_8']
        
        for level in levels:
            if level in result and result[level]:
                classification[f'{level}_name'] = result[level].get('name', '')
                classification[f'{level}_chemont_id'] = result[level].get('chemont_id', '')
        
        # 提取其他信息
        if 'smiles' in result:
            classification['canonical_smiles'] = result['smiles']
        if 'inchikey' in result:
            classification['inchikey'] = result['inchikey']
        if 'molecular_formula' in result:
            classification['molecular_formula'] = result['molecular_formula']
            
        # 提取直接父类信息
        if 'direct_parent' in result and result['direct_parent']:
            classification['direct_parent_name'] = result['direct_parent'].get('name', '')
            classification['direct_parent_chemont_id'] = result['direct_parent'].get('chemont_id', '')
            
        return classification
    
    def query_batch(self, query_ids: List[str], delay: float = 1.0) -> List[Dict]:
        """
        批量查询ClassyFire结果
        
        Args:
            query_ids: 查询ID列表
            delay: 查询间隔时间（秒）
            
        Returns:
            结果列表
        """
        results = []
        
        logger.info(f"开始批量查询 {len(query_ids)} 个任务")
        
        for i, query_id in enumerate(query_ids):
            logger.info(f"查询进度: {i+1}/{len(query_ids)} - {query_id}")
            
            result = self.query_single(query_id.strip())
            if result:
                results.append(result)
                
                if result['status'] == 'completed':
                    logger.info(f"✓ 完成: {query_id}")
                elif result['status'] == 'pending':
                    logger.info(f"⏳ 处理中: {query_id}")
                else:
                    logger.warning(f"✗ 失败: {query_id} - {result['status']}")
            
            # 控制查询频率
            if i < len(query_ids) - 1:
                time.sleep(delay)
        
        return results
    
    def save_results_to_excel(self, results: List[Dict], output_file: str):
        """保存结果到Excel文件"""
        data = []
        
        for result in results:
            row = {
                'query_id': result['query_id'],
                'status': result['status']
            }
            
            if result['classification']:
                row.update(result['classification'])
            
            data.append(row)
        
        df = pd.DataFrame(data)
        df.to_excel(output_file, index=False)
        logger.info(f"结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='ClassyFire查询工具')
    parser.add_argument('--query_id', type=str, help='单个查询ID')
    parser.add_argument('--batch_file', type=str, help='包含查询ID的文本文件（每行一个ID）')
    parser.add_argument('--from_checkpoint', action='store_true', help='从检查点文件查询')
    parser.add_argument('--output', type=str, default='classyfire_query_results.xlsx', help='输出文件路径')
    parser.add_argument('--delay', type=float, default=1.0, help='查询间隔时间（秒）')
    
    args = parser.parse_args()
    
    query_tool = ClassyFireQuery()
    
    if args.query_id:
        # 单个查询
        logger.info(f"查询单个ID: {args.query_id}")
        result = query_tool.query_single(args.query_id)
        
        if result:
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result['status'] == 'completed':
                print("\n分类信息:")
                classification = result['classification']
                for key, value in classification.items():
                    if value:
                        print(f"  {key}: {value}")
        
    elif args.batch_file:
        # 批量查询
        try:
            with open(args.batch_file, 'r') as f:
                query_ids = [line.strip() for line in f if line.strip()]
            
            logger.info(f"从文件读取 {len(query_ids)} 个查询ID")
            results = query_tool.query_batch(query_ids, args.delay)
            
            # 保存结果
            query_tool.save_results_to_excel(results, args.output)
            
            # 统计信息
            status_counts = {}
            for result in results:
                status = result['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            print("\n查询统计:")
            for status, count in status_counts.items():
                print(f"  {status}: {count}")
                
        except FileNotFoundError:
            logger.error(f"文件不存在: {args.batch_file}")
        except Exception as e:
            logger.error(f"批量查询失败: {str(e)}")
    
    elif args.from_checkpoint:
        # 从检查点查询
        try:
            import pickle
            
            with open('classyfire_status.pkl', 'rb') as f:
                status_data = pickle.load(f)
            
            submitted = status_data.get('submitted', {})
            query_ids = [info['query_id'] for info in submitted.values()]
            
            logger.info(f"从检查点读取 {len(query_ids)} 个查询ID")
            results = query_tool.query_batch(query_ids, args.delay)
            
            # 保存结果
            query_tool.save_results_to_excel(results, args.output)
            
        except FileNotFoundError:
            logger.error("检查点文件不存在: classyfire_status.pkl")
        except Exception as e:
            logger.error(f"从检查点查询失败: {str(e)}")
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
