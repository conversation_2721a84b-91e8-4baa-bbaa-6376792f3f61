import pandas as pd
import requests
import time
import json
from typing import Dict, Optional, List
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClassyFireClassifier:
    """
    使用ClassyFire API对化学品进行分类的工具类
    """
    
    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        
    def classify_compound(self, smiles: str, max_retries: int = 3, delay: float = 1.0) -> Optional[Dict]:
        """
        对单个化合物进行ClassyFire分类
        
        Args:
            smiles: SMILES字符串
            max_retries: 最大重试次数
            delay: 请求间隔时间（秒）
            
        Returns:
            分类结果字典，包含各级分类信息
        """
        if pd.isna(smiles) or not smiles.strip():
            return None
            
        # 清理SMILES字符串
        smiles = smiles.strip()
        
        for attempt in range(max_retries):
            try:
                # 第一步：提交SMILES进行分类
                submit_url = f"{self.base_url}/entities.json"
                data = {
                    'label': 'compound',
                    'smiles': smiles,
                    'structure_source': 'SMILES'
                }
                
                response = self.session.post(submit_url, data=data, timeout=30)
                
                if response.status_code == 201:
                    result = response.json()
                    query_id = result.get('id')
                    
                    if query_id:
                        # 第二步：获取分类结果
                        classification = self._get_classification_result(query_id)
                        if classification:
                            return classification
                
                # 如果失败，等待后重试
                if attempt < max_retries - 1:
                    time.sleep(delay * (attempt + 1))
                    
            except Exception as e:
                logger.warning(f"分类SMILES '{smiles}' 时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(delay * (attempt + 1))
                    
        logger.error(f"无法分类SMILES: {smiles}")
        return None
    
    def _get_classification_result(self, query_id: str, max_wait: int = 60) -> Optional[Dict]:
        """
        获取ClassyFire分类结果
        
        Args:
            query_id: 查询ID
            max_wait: 最大等待时间（秒）
            
        Returns:
            分类结果字典
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                result_url = f"{self.base_url}/entities/{query_id}.json"
                response = self.session.get(result_url, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 检查是否有分类信息
                    if 'kingdom' in result or 'superclass' in result:
                        return self._extract_classification_info(result)
                
                # 等待处理完成
                time.sleep(2)
                
            except Exception as e:
                logger.warning(f"获取结果时出错: {str(e)}")
                time.sleep(2)
                
        return None
    
    def _extract_classification_info(self, result: Dict) -> Dict:
        """
        从ClassyFire结果中提取分类信息
        
        Args:
            result: ClassyFire API返回的原始结果
            
        Returns:
            整理后的分类信息字典
        """
        classification = {}
        
        # 提取各级分类信息
        hierarchy_levels = [
            'kingdom', 'superclass', 'class', 'subclass', 
            'molecular_framework', 'alternative_parents', 
            'substituents', 'direct_parent'
        ]
        
        for level in hierarchy_levels:
            if level in result and result[level]:
                if level == 'alternative_parents':
                    # 处理替代父类（可能有多个）
                    if isinstance(result[level], list) and result[level]:
                        names = [item.get('name', '') for item in result[level] if item.get('name')]
                        classification[f"{level}_names"] = '; '.join(names[:3])  # 取前3个
                elif level == 'substituents':
                    # 处理取代基（可能有多个）
                    if isinstance(result[level], list) and result[level]:
                        names = [item.get('name', '') for item in result[level] if item.get('name')]
                        classification[f"{level}_names"] = '; '.join(names[:5])  # 取前5个
                else:
                    # 处理单个分类项
                    if isinstance(result[level], dict):
                        classification[f"{level}_name"] = result[level].get('name', '')
                        classification[f"{level}_chemont_id"] = result[level].get('chemont_id', '')
        
        # 添加其他有用信息
        if 'smiles' in result:
            classification['canonical_smiles'] = result['smiles']
        if 'inchikey' in result:
            classification['inchikey'] = result['inchikey']
        if 'molecular_formula' in result:
            classification['molecular_formula'] = result['molecular_formula']
            
        return classification

def classify_excel_compounds(input_file: str, 
                           output_file: str, 
                           smiles_column: str = 'smiles',
                           batch_delay: float = 1.0) -> None:
    """
    对Excel文件中的化合物进行批量ClassyFire分类
    
    Args:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径
        smiles_column: SMILES列名
        batch_delay: 批处理延迟时间（秒）
    """
    
    # 读取Excel文件
    try:
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共 {len(df)} 行数据")
    except Exception as e:
        logger.error(f"读取文件失败: {str(e)}")
        return
    
    # 检查SMILES列是否存在
    if smiles_column not in df.columns:
        logger.error(f"未找到列 '{smiles_column}', 可用列: {list(df.columns)}")
        return
    
    # 初始化分类器
    classifier = ClassyFireClassifier()
    
    # 准备结果列
    classification_columns = [
        'kingdom_name', 'kingdom_chemont_id',
        'superclass_name', 'superclass_chemont_id',
        'class_name', 'class_chemont_id',
        'subclass_name', 'subclass_chemont_id',
        'molecular_framework_name', 'molecular_framework_chemont_id',
        'direct_parent_name', 'direct_parent_chemont_id',
        'alternative_parents_names', 'substituents_names',
        'canonical_smiles', 'inchikey', 'molecular_formula',
        'classification_status'
    ]
    
    # 初始化结果列
    for col in classification_columns:
        df[col] = None
    
    # 批量处理
    total_compounds = len(df)
    processed = 0
    
    logger.info(f"开始处理 {total_compounds} 个化合物...")
    
    for idx, row in df.iterrows():
        smiles = row[smiles_column]
        
        logger.info(f"处理化合物 {processed + 1}/{total_compounds}: {smiles}")
        
        # 进行分类
        classification = classifier.classify_compound(smiles)
        
        if classification:
            # 填充分类结果
            for col in classification_columns[:-1]:  # 除了status列
                if col in classification:
                    df.at[idx, col] = classification[col]
            df.at[idx, 'classification_status'] = 'Success'
            logger.info(f"分类成功: {classification.get('kingdom_name', 'Unknown')}")
        else:
            df.at[idx, 'classification_status'] = 'Failed'
            logger.warning(f"分类失败")
        
        processed += 1
        
        # 添加延迟以避免过于频繁的API调用
        if processed < total_compounds:
            time.sleep(batch_delay)
    
    # 保存结果
    try:
        df.to_excel(output_file, index=False)
        logger.info(f"结果已保存到: {output_file}")
        
        # 打印统计信息
        success_count = len(df[df['classification_status'] == 'Success'])
        logger.info(f"分类统计: {success_count}/{total_compounds} 成功")
        
    except Exception as e:
        logger.error(f"保存文件失败: {str(e)}")

# 使用示例
if __name__ == "__main__":
    # 配置参数
    INPUT_FILE = "input_compounds.xlsx"  # 输入文件路径
    OUTPUT_FILE = "classified_compounds.xlsx"  # 输出文件路径
    SMILES_COLUMN = "smiles"  # SMILES列名，根据实际情况修改
    
    # 执行分类
    classify_excel_compounds(
        input_file=INPUT_FILE,
        output_file=OUTPUT_FILE,
        smiles_column=SMILES_COLUMN,
        batch_delay=1.0  # API调用间隔时间（秒）
    )
    
    print("分类完成！")

# 如果需要只处理特定行，可以使用以下函数
def classify_specific_rows(input_file: str, 
                          output_file: str, 
                          start_row: int = 0, 
                          end_row: Optional[int] = None,
                          smiles_column: str = 'smiles') -> None:
    """
    处理特定行范围的化合物
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径  
        start_row: 开始行（从0开始）
        end_row: 结束行（不包括），None表示到文件末尾
        smiles_column: SMILES列名
    """
    df = pd.read_excel(input_file)
    
    if end_row is None:
        end_row = len(df)
    
    # 只处理指定范围
    subset_df = df.iloc[start_row:end_row].copy()
    
    # 创建临时文件进行处理
    temp_file = "temp_subset.xlsx"
    subset_df.to_excel(temp_file, index=False)
    
    # 分类处理
    classify_excel_compounds(temp_file, output_file, smiles_column)
    
    # 清理临时文件
    import os
    if os.path.exists(temp_file):
        os.remove(temp_file)