#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ClassyFire化学品分类启动脚本
提供简单的命令行界面
"""

import os
import sys
import argparse
from config import *
from classyfire_complete import ClassyFireProcessor

def check_requirements():
    """检查运行环境和依赖"""
    required_packages = ['pandas', 'requests', 'openpyxl']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少必需的包: {', '.join(missing_packages)}")
        print(f"请运行: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_input_file():
    """检查输入文件"""
    if not os.path.exists(INPUT_FILE):
        print(f"错误: 输入文件不存在: {INPUT_FILE}")
        return False
    
    try:
        import pandas as pd
        df = pd.read_excel(INPUT_FILE)
        
        if SMILES_COLUMN not in df.columns:
            print(f"错误: 输入文件中没有找到SMILES列: {SMILES_COLUMN}")
            print(f"可用列: {list(df.columns)}")
            return False
        
        print(f"✓ 输入文件检查通过: {len(df)} 行数据")
        return True
        
    except Exception as e:
        print(f"错误: 无法读取输入文件: {str(e)}")
        return False

def show_status():
    """显示当前状态"""
    processor = ClassyFireProcessor()
    status_data = processor.load_status()
    results = processor.load_results()
    
    if not status_data.get('submitted'):
        print("没有找到进行中的任务")
        return
    
    submitted = status_data['submitted']
    total_submitted = len(submitted)
    total_completed = len(results)
    
    print(f"任务状态:")
    print(f"  总提交数: {total_submitted}")
    print(f"  已完成数: {total_completed}")
    print(f"  完成率: {total_completed/total_submitted*100:.1f}%")
    
    # 统计各状态
    status_counts = {}
    for info in submitted.values():
        status = info.get('status', 'unknown')
        status_counts[status] = status_counts.get(status, 0) + 1
    
    print(f"  状态分布:")
    for status, count in status_counts.items():
        print(f"    {status}: {count}")

def clean_files():
    """清理临时文件"""
    files_to_clean = [STATUS_FILE, RESULTS_FILE, LOG_FILE, REPORT_FILE]
    
    cleaned = 0
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✓ 已删除: {file_path}")
                cleaned += 1
            except Exception as e:
                print(f"✗ 删除失败 {file_path}: {str(e)}")
    
    if cleaned == 0:
        print("没有找到需要清理的文件")
    else:
        print(f"共清理了 {cleaned} 个文件")

def main():
    parser = argparse.ArgumentParser(description='ClassyFire化学品分类工具')
    parser.add_argument('--start', action='store_true', help='开始分类处理')
    parser.add_argument('--resume', action='store_true', help='从检查点恢复')
    parser.add_argument('--status', action='store_true', help='显示当前状态')
    parser.add_argument('--clean', action='store_true', help='清理临时文件')
    parser.add_argument('--query', type=str, help='查询特定ID的结果')
    parser.add_argument('--start_idx', type=int, default=START_INDEX, help='开始索引')
    parser.add_argument('--max_hours', type=int, default=MAX_WAIT_HOURS, help='最大等待时间（小时）')
    
    args = parser.parse_args()
    
    # 显示配置信息
    print("ClassyFire化学品分类工具")
    print("=" * 50)
    print(f"输入文件: {INPUT_FILE}")
    print(f"输出文件: {OUTPUT_FILE}")
    print(f"SMILES列: {SMILES_COLUMN}")
    print("=" * 50)
    
    if args.status:
        show_status()
        return
    
    if args.clean:
        clean_files()
        return
    
    if args.query:
        from classyfire_query import ClassyFireQuery
        query_tool = ClassyFireQuery()
        result = query_tool.query_single(args.query)
        
        if result:
            import json
            print(json.dumps(result, indent=2, ensure_ascii=False))
        return
    
    # 检查运行环境
    if not check_requirements():
        return
    
    if not check_input_file():
        return
    
    # 创建处理器
    processor = ClassyFireProcessor()
    
    if args.resume:
        # 从检查点恢复
        status_data = processor.load_status()
        if not status_data.get('submitted'):
            print("没有找到可恢复的任务")
            return
        
        print("从检查点恢复处理...")
        results = processor.monitor_progress(args.max_hours)
        
        # 导出结果
        import pandas as pd
        df = pd.read_excel(INPUT_FILE)
        processor.export_results_to_excel(results, df, OUTPUT_FILE, SMILES_COLUMN)
        processor.generate_summary_report(status_data['submitted'], results)
        
    elif args.start:
        # 开始新的处理
        status_data = processor.load_status()
        if status_data.get('submitted'):
            choice = input("发现未完成的任务，是否清理后重新开始？(y/n): ").lower()
            if choice != 'y':
                print("已取消")
                return
            clean_files()
        
        print("开始新的分类处理...")
        processor.process_excel_file(
            input_file=INPUT_FILE,
            output_file=OUTPUT_FILE,
            smiles_column=SMILES_COLUMN,
            id_column=ID_COLUMN,
            start_idx=args.start_idx,
            max_wait_hours=args.max_hours
        )
    
    else:
        # 交互式菜单
        while True:
            print("\n请选择操作:")
            print("1. 开始新的分类处理")
            print("2. 从检查点恢复")
            print("3. 显示当前状态")
            print("4. 清理临时文件")
            print("5. 退出")
            
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == '1':
                # 检查是否有未完成任务
                status_data = processor.load_status()
                if status_data.get('submitted'):
                    confirm = input("发现未完成的任务，是否清理后重新开始？(y/n): ").lower()
                    if confirm != 'y':
                        continue
                    clean_files()
                
                processor.process_excel_file(
                    input_file=INPUT_FILE,
                    output_file=OUTPUT_FILE,
                    smiles_column=SMILES_COLUMN,
                    id_column=ID_COLUMN,
                    start_idx=args.start_idx,
                    max_wait_hours=args.max_hours
                )
                break
                
            elif choice == '2':
                status_data = processor.load_status()
                if not status_data.get('submitted'):
                    print("没有找到可恢复的任务")
                    continue
                
                results = processor.monitor_progress(args.max_hours)
                
                import pandas as pd
                df = pd.read_excel(INPUT_FILE)
                processor.export_results_to_excel(results, df, OUTPUT_FILE, SMILES_COLUMN)
                processor.generate_summary_report(status_data['submitted'], results)
                break
                
            elif choice == '3':
                show_status()
                
            elif choice == '4':
                clean_files()
                
            elif choice == '5':
                print("退出")
                break
                
            else:
                print("无效选择，请重新输入")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断，进度已保存")
    except Exception as e:
        print(f"程序出错: {str(e)}")
        import traceback
        traceback.print_exc()
