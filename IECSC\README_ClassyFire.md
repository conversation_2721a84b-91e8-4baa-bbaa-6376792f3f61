# ClassyFire化学品分类工具套件

## 概述

这是一套完整的ClassyFire化学品分类工具，包括批量提交、监控、查询和结果导出功能。

## 文件说明

### 1. `classyfire_complete.py` - 完整处理工具
- **功能**：批量提交SMILES到ClassyFire，监控分类进度，导出结果
- **特点**：支持断点续传、错误恢复、进度监控
- **适用场景**：大批量化合物分类的完整流程

### 2. `classyfire_query.py` - 查询工具
- **功能**：查询已提交的ClassyFire任务状态和结果
- **特点**：支持单个查询、批量查询、从检查点查询
- **适用场景**：检查任务状态、获取分类结果

## 使用方法

### 方法一：完整处理流程

1. **配置参数**
```python
# 在 classyfire_complete.py 中修改以下配置
INPUT_FILE = 'your_input.xlsx'  # 输入Excel文件
OUTPUT_FILE = 'your_output.xlsx'  # 输出Excel文件
SMILES_COLUMN = 'smiles'  # SMILES列名
START_INDEX = 0  # 开始处理的行索引
MAX_WAIT_HOURS = 48  # 最大等待时间（小时）
```

2. **运行完整流程**
```bash
python classyfire_complete.py
```

3. **从断点恢复**
如果处理中断，再次运行程序会提示是否从检查点恢复。

### 方法二：分步处理

#### 步骤1：提交化合物
```python
from classyfire_complete import ClassyFireProcessor
import pandas as pd

processor = ClassyFireProcessor()
df = pd.read_excel('your_input.xlsx')

# 批量提交
submitted = processor.submit_batch(df, 'smiles')
```

#### 步骤2：监控进度
```python
# 监控分类进度
results = processor.monitor_progress(max_wait_hours=24)
```

#### 步骤3：导出结果
```python
# 导出到Excel
processor.export_results_to_excel(results, df, 'output.xlsx')
```

### 方法三：查询已提交的任务

#### 单个查询
```bash
python classyfire_query.py --query_id YOUR_QUERY_ID
```

#### 批量查询
```bash
# 创建包含查询ID的文本文件（每行一个ID）
echo "12345" > query_ids.txt
echo "12346" >> query_ids.txt

# 批量查询
python classyfire_query.py --batch_file query_ids.txt --output results.xlsx
```

#### 从检查点查询
```bash
python classyfire_query.py --from_checkpoint --output results.xlsx
```

## 输入文件格式

Excel文件必须包含SMILES列，例如：

| smiles | compound_name | other_info |
|--------|---------------|------------|
| CCO | ethanol | ... |
| c1ccccc1 | benzene | ... |
| CC(=O)O | acetic acid | ... |

## 输出文件格式

输出Excel文件包含原始数据和分类结果：

| smiles | kingdom_name | superclass_name | class_name | ... | classyfire_query_id | classification_status |
|--------|--------------|-----------------|------------|-----|--------------------|--------------------|
| CCO | Organic compounds | Organoheterocyclic compounds | Alcohols and polyols | ... | 12345 | completed |

## 分类信息字段

- **kingdom_name/chemont_id**: 界
- **superclass_name/chemont_id**: 超类
- **class_name/chemont_id**: 类
- **subclass_name/chemont_id**: 亚类
- **level_5-8_name/chemont_id**: 更细分的分类级别
- **direct_parent_name/chemont_id**: 直接父类
- **alternative_parents**: 替代父类
- **canonical_smiles**: 标准SMILES
- **inchikey**: InChI Key
- **molecular_formula**: 分子式

## 重要特性

### 1. 断点续传
- 程序会自动保存进度到 `classyfire_status.pkl`
- 中断后重新运行可以从断点继续

### 2. 错误处理
- 自动跳过无效SMILES
- 处理API限制和网络错误
- 详细的日志记录

### 3. 智能过滤
- 自动检测过长的SMILES（>2000字符）
- 检测含有过多氟原子的化合物（>100个F）
- 可配置的过滤参数

### 4. 频率控制
- 自动控制API请求频率
- 避免触发ClassyFire的频率限制
- 每100个化合物暂停30秒

## 配置参数说明

### API限制参数
```python
max_batch_size = 100      # 每批最大化合物数量
request_delay = 2.0       # 请求间隔（秒）
max_retries = 3           # 最大重试次数
timeout = 30              # 请求超时时间
```

### 验证参数
```python
max_smiles_length = 2000  # SMILES最大长度
max_fluorine_count = 100  # 最大氟原子数量
```

## 状态文件说明

- **classyfire_status.pkl**: 保存提交状态和查询ID
- **classyfire_results.pkl**: 保存分类结果
- **classyfire.log**: 详细日志文件
- **classyfire_report.json**: 统计报告

## 故障排除

### 1. 网络连接问题
- 确保能够访问 http://classyfire.wishartlab.com
- 检查防火墙设置

### 2. API频率限制
- 程序会自动处理频率限制
- 如果频繁出现429错误，可以增加 `request_delay` 参数

### 3. 内存不足
- 对于大文件，可以分批处理
- 使用 `START_INDEX` 参数分段处理

### 4. 分类失败
- 检查SMILES格式是否正确
- 某些复杂化合物可能无法分类

## 注意事项

1. **ClassyFire是免费服务**，请合理使用，避免过度请求
2. **分类需要时间**，复杂化合物可能需要几分钟到几小时
3. **网络稳定性**很重要，建议在稳定的网络环境下运行
4. **大批量处理**建议分批进行，每批不超过1000个化合物
5. **定期备份**状态文件，避免意外丢失进度

## 示例用法

```python
# 完整示例
from classyfire_complete import ClassyFireProcessor
import pandas as pd

# 创建处理器
processor = ClassyFireProcessor()

# 处理Excel文件
processor.process_excel_file(
    input_file='compounds.xlsx',
    output_file='classified_compounds.xlsx',
    smiles_column='smiles',
    max_wait_hours=24
)
```

## 联系和支持

如果遇到问题，请检查：
1. 日志文件 `classyfire.log`
2. ClassyFire官方文档
3. 网络连接状态
