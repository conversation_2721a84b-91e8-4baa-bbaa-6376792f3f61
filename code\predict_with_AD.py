#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于SMILES的化学品毒性预测与应用域判断整合脚本

功能:
1. 对化学品进行毒性预测（仅基于SMILES）
2. 使用预设的最佳参数判断是否在应用域内
3. 输出整合结果

使用方法:
1. 在下面的配置区域设置你的参数
2. 运行: python predict_with_AD.py
"""

import pandas as pd
import numpy as np
import os
import sys

import torch
import torch.nn.functional as F
from rdkit import Chem
import warnings
warnings.filterwarnings('ignore')

# 设置使用CUDA:1（与训练时一致）
if torch.cuda.is_available():
    torch.cuda.set_device(1)
    torch.set_default_tensor_type('torch.cuda.FloatTensor')
    print(f"✅ 使用GPU 1: {torch.cuda.get_device_name(1)}")
else:
    torch.set_default_tensor_type('torch.FloatTensor')
    print("⚠️ CUDA不可用，使用CPU")

# 导入模块
from AFPModel import prepare_smiles_data

# 尝试导入应用域判断模块（可选）
try:
    from adsal import NSG
    HAS_ADSAL = True
except ImportError:
    print("⚠️ 警告: 未找到adsal模块，应用域判断功能将不可用")
    HAS_ADSAL = False
    NSG = None

# =============================================================================
# 配置参数 - 请在这里修改你的设置
# =============================================================================

# 预测模式选择：'file', 'single'
PREDICTION_MODE = 'file'

# 输入输出文件配置
INPUT_FILE = 'pre_compounds.xlsx'        # 输入的化合物文件
OUTPUT_FILE = 'prediction_with_AD_results.xlsx'  # 输出文件名

# 单个SMILES预测参数（当PREDICTION_MODE='single'时使用）
SINGLE_SMILES = 'CCO'  # 要预测的SMILES

# 最佳应用域参数
OPTIMAL_DENSLB = 0.15    # 最佳相似性密度阈值 
OPTIMAL_LDUB = 0.8     # 最佳局域不连续性阈值

# 模型和数据路径（通常不需要修改）
MODEL_PATH = 'saved_model/global_best_model.pt'
TRAINING_DATA_PATH = 'TrainingSet.xlsx'

# 批处理参数
BATCH_SIZE = 256

# 设备配置
USE_CUDA = True  # 是否使用CUDA（推荐，模型在CUDA:1上训练）

# =============================================================================
# 注意：
# 1. 请确保你已经通过运行AD/AD.py获得了最佳参数
# 2. 将获得的最佳densLB和LdUB值填入上面的OPTIMAL_DENSLB和OPTIMAL_LDUB
# 3. 修改INPUT_FILE为你要分析的化合物文件路径
# 4. 运行: python predict_with_AD.py
# =============================================================================

# =============================================================================
# 预测功能类和函数
# =============================================================================

class SMILESPredictor:
    """基于SMILES的鱼类急性毒性预测器"""

    def __init__(self, model_path, use_cuda=True):
        """初始化预测器"""
        self.model_path = model_path
        self.use_cuda = use_cuda and torch.cuda.is_available()

        if self.use_cuda:
            # 设置使用cuda:1（与训练时一致）
            torch.cuda.set_device(1)
            self.device = torch.device('cuda:1')
        else:
            self.device = torch.device('cpu')

        self._load_model()

    def _load_model(self):
        """加载模型和相关组件"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        print(f"🔄 加载模型: {self.model_path}")
        print(f"📱 使用设备: {self.device}")

        # 加载模型到指定设备
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        except TypeError:
            checkpoint = torch.load(self.model_path, map_location=self.device)

        self.feature_dicts = checkpoint['feature_dicts']

        # 重新构建模型（确保设备一致性）
        model_config = checkpoint['model_config']

        # 导入模型类
        from AFPModel import SMILESFingerprint
        from AttentiveFP import get_smiles_array

        # 获取特征维度
        sample_smiles = list(self.feature_dicts['smiles_to_atom_mask'].keys())[0]
        x_atom, x_bonds, _, _, _, _ = get_smiles_array([sample_smiles], self.feature_dicts)
        input_feature_dim = x_atom.shape[-1]
        input_bond_dim = x_bonds.shape[-1]

        # 重新构建模型
        self.model = SMILESFingerprint(
            radius=model_config['radius'],
            T=model_config['T'],
            input_feature_dim=input_feature_dim,
            input_bond_dim=input_bond_dim,
            fingerprint_dim=model_config['fingerprint_dim'],
            output_units_num=model_config['output_units_num'],
            p_dropout=model_config['p_dropout']
        )

        # 加载模型权重
        self.model.load_state_dict(checkpoint['model_state_dict'])

        # 移动模型到指定设备
        self.model.to(self.device)
        self.model.eval()

        print(f"✅ 模型加载成功 (设备: {self.device})")

    def _validate_smiles(self, smiles):
        """验证SMILES字符串"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False, "无效的SMILES字符串"

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
            return True, canonical_smiles
        except Exception as e:
            return False, f"SMILES处理错误: {str(e)}"

    def predict_single(self, smiles):
        """预测单个SMILES"""
        is_valid, result = self._validate_smiles(smiles)
        if not is_valid:
            return {'smiles': smiles, 'error': result}

        canonical_smiles = result

        try:
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
                [canonical_smiles], self.feature_dicts
            )

            # 转换为tensor并移动到指定设备
            x_atom = torch.Tensor(x_atom).to(self.device)
            x_bonds = torch.Tensor(x_bonds).to(self.device)
            x_atom_index = torch.LongTensor(x_atom_index).to(self.device)
            x_bond_index = torch.LongTensor(x_bond_index).to(self.device)
            x_mask = torch.Tensor(x_mask).to(self.device)

            with torch.no_grad():
                _, mol_prediction = self.model(
                    x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
                )

                y_pred = mol_prediction[:, 0:2]
                y_pred_prob = F.softmax(y_pred, dim=-1).data.cpu().numpy()[0]

                prediction = int(y_pred_prob[1] > 0.5)
                probability = float(y_pred_prob[1])

            result = {
                'smiles': smiles,
                'canonical_smiles': canonical_smiles,
                'prediction': prediction,
                'probability': probability,
                'prediction_label': '高毒性' if prediction == 1 else '低毒性',
                'error': None
            }

            return result

        except Exception as e:
            return {'smiles': smiles, 'error': f"预测错误: {str(e)}"}

def predict_on_input_file(input_file_path, model_path=None, output_file_path=None):
    """
    对输入文件进行预测，并将预测值和预测概率保存在原输入文件中

    Args:
        input_file_path: 输入文件路径（Excel格式）
        model_path: 模型文件路径，如果为None则使用默认模型
        output_file_path: 输出文件路径，如果为None则在原文件名后加_predicted
    """
    print("开始对输入文件进行预测...")

    # 1. 检查输入文件
    if not os.path.exists(input_file_path):
        print(f"输入文件不存在: {input_file_path}")
        return

    # 2. 确定模型路径
    if model_path is None:
        model_path = 'save_model/global_best_model.pt'

    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        return

    # 3. 确定输出文件路径
    if output_file_path is None:
        base_name = os.path.splitext(input_file_path)[0]
        output_file_path = f"{base_name}_predicted.xlsx"

    print(f"输入文件: {input_file_path}")
    print(f"模型文件: {model_path}")
    print(f"输出文件: {output_file_path}")

    # 设置使用CUDA:1（与训练时一致）
    use_cuda = USE_CUDA and torch.cuda.is_available()
    if use_cuda:
        torch.cuda.set_device(1)
        device = torch.device('cuda:1')
    else:
        device = torch.device('cpu')
    print(f"使用设备: {device}")

    # 4. 加载模型
    try:
        # 加载模型到指定设备
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        except TypeError:
            checkpoint = torch.load(model_path, map_location=device)

        feature_dicts = checkpoint['feature_dicts']

        # 重新构建模型（确保设备一致性）
        model_config = checkpoint['model_config']

        # 导入模型类
        from AFPModel import SMILESFingerprint
        from AttentiveFP import get_smiles_array

        # 获取特征维度
        sample_smiles = list(feature_dicts['smiles_to_atom_mask'].keys())[0]
        x_atom, x_bonds, _, _, _, _ = get_smiles_array([sample_smiles], feature_dicts)
        input_feature_dim = x_atom.shape[-1]
        input_bond_dim = x_bonds.shape[-1]

        # 重新构建模型
        model = SMILESFingerprint(
            radius=model_config['radius'],
            T=model_config['T'],
            input_feature_dim=input_feature_dim,
            input_bond_dim=input_bond_dim,
            fingerprint_dim=model_config['fingerprint_dim'],
            output_units_num=model_config['output_units_num'],
            p_dropout=model_config['p_dropout']
        )

        # 加载模型权重
        model.load_state_dict(checkpoint['model_state_dict'])

        # 移动模型到指定设备
        model.to(device)
        model.eval()

        print(f"模型加载成功（设备: {device}）")
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return

    # 5. 加载输入数据
    try:
        df = pd.read_excel(input_file_path)
        print(f"样本数量: {len(df)}")

        # 检查必要列
        required_columns = ['smiles']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"输入数据缺少必要列: {missing_columns}")
            return

    except Exception as e:
        print(f"数据加载失败: {str(e)}")
        return

    # 6. 准备结果DataFrame
    result_df = df.copy()
    result_df['canonical_smiles'] = ''
    result_df['prediction'] = -1
    result_df['probability'] = 0.0
    result_df['prediction_label'] = ''
    result_df['error'] = ''

    # 7. 验证SMILES并进行预测
    print("开始预测...")
    valid_indices = []
    valid_canonical_smiles = []

    for idx, row in df.iterrows():
        smiles = row['smiles']
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                result_df.loc[idx, 'error'] = "无效的SMILES字符串"
                continue

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
            result_df.loc[idx, 'canonical_smiles'] = canonical_smiles
            valid_indices.append(idx)
            valid_canonical_smiles.append(canonical_smiles)

        except Exception as e:
            result_df.loc[idx, 'error'] = f"SMILES处理错误: {str(e)}"

    print(f"有效样本: {len(valid_indices)}/{len(df)}")

    if len(valid_indices) == 0:
        print("没有有效的样本可以预测")
        return

    # 8. 批量预测
    try:
        valid_df = df.loc[valid_indices].copy()
        valid_df['smiles'] = valid_canonical_smiles

        batch_size = BATCH_SIZE
        batch_list = [valid_indices[i:i+batch_size] for i in range(0, len(valid_indices), batch_size)]

        for batch_idx, batch_indices in enumerate(batch_list):
            print(f"处理批次 {batch_idx + 1}/{len(batch_list)}")

            batch_smiles = [valid_canonical_smiles[valid_indices.index(idx)] for idx in batch_indices]

            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
                batch_smiles, feature_dicts
            )

            # 转换为tensor并移动到指定设备
            x_atom = torch.Tensor(x_atom).to(device)
            x_bonds = torch.Tensor(x_bonds).to(device)
            x_atom_index = torch.LongTensor(x_atom_index).to(device)
            x_bond_index = torch.LongTensor(x_bond_index).to(device)
            x_mask = torch.Tensor(x_mask).to(device)

            # 预测
            with torch.no_grad():
                _, mol_prediction = model(
                    x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
                )

                y_pred = mol_prediction[:, 0:2]
                y_pred_prob = F.softmax(y_pred, dim=-1).data.cpu().numpy()

                # 保存预测结果
                for i, orig_idx in enumerate(batch_indices):
                    probability = float(y_pred_prob[i, 1])
                    prediction = int(probability > 0.5)

                    result_df.loc[orig_idx, 'prediction'] = prediction
                    result_df.loc[orig_idx, 'probability'] = probability
                    result_df.loc[orig_idx, 'prediction_label'] = '有毒' if prediction == 1 else '无毒'

                # 清理GPU内存
                if use_cuda:
                    torch.cuda.empty_cache()

        print("预测完成")

    except Exception as e:
        print(f"预测过程出错: {str(e)}")
        # 为所有有效索引标记错误
        for idx in valid_indices:
            result_df.loc[idx, 'error'] = f"预测错误: {str(e)}"

    # 9. 保存结果
    try:
        result_df.to_excel(output_file_path, index=False)
        print(f"结果已保存到: {output_file_path}")

        # 显示统计结果
        valid_predictions = result_df[result_df['error'] == '']
        if len(valid_predictions) > 0:
            high_toxicity_count = (valid_predictions['prediction'] == 1).sum()
            low_toxicity_count = (valid_predictions['prediction'] == 0).sum()

            print(f"\n预测统计:")
            print(f"有效预测: {len(valid_predictions)}/{len(result_df)}")
            print(f"高毒性: {high_toxicity_count} ({high_toxicity_count/len(valid_predictions)*100:.1f}%)")
            print(f"低毒性: {low_toxicity_count} ({low_toxicity_count/len(valid_predictions)*100:.1f}%)")
            print(f"平均毒性概率: {valid_predictions['probability'].mean():.4f}")

        errors = result_df[result_df['error'] != '']
        if len(errors) > 0:
            print(f"无法预测样本: {len(errors)}")

    except Exception as e:
        print(f"保存结果失败: {str(e)}")

# exp权重函数
def expWt(x, a=15, eps=1e-6):
    """指数权重函数"""
    return np.exp(-a*(1-x)/(x + eps))

EXP_WEIGHT_PARAMS = {'a': 15}

# =============================================================================
# 核心功能函数
# =============================================================================

def load_training_data(file_path):
    """加载训练集数据"""
    print(f"加载训练集数据: {file_path}")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"训练数据文件不存在: {file_path}")

    df = pd.read_excel(file_path)

    # 检查必要列
    required_cols = ['smiles', 'y']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"训练集缺少必要列: {missing_cols}")

    # 准备数据
    df_clean = df[['smiles', 'y']].copy()
    df_clean.reset_index(drop=True, inplace=True)

    print(f"训练集样本数: {len(df_clean)}")
    return df_clean

def run_prediction(input_file, model_path, temp_output):
    """运行毒性预测"""
    print("步骤1: 运行毒性预测")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

    # 运行预测
    predict_on_input_file(input_file, model_path, temp_output)

    if not os.path.exists(temp_output):
        raise RuntimeError("预测失败，未生成预测文件")

    print("预测完成")
    return temp_output

def calculate_ad_metrics(df_train, df_query):
    """计算应用域指标"""
    print("步骤2: 计算应用域指标")

    # 创建NSG对象
    nsg = NSG(df_train, yCol='y', smiCol='smiles')

    # 计算分子指纹相似性
    nsg.calcPairwiseSimilarityWithFp('MACCS_keys')

    # 生成查询-训练相似性矩阵
    dfQTSM = nsg.genQTSM(df_query, 'smiles')

    # 计算应用域指标（使用exp权重函数）
    ad_metrics = nsg.queryADMetrics(
        dfQTSM,
        wtFunc1=expWt,
        kw1=EXP_WEIGHT_PARAMS,
        wtFunc2=expWt,
        kw2=EXP_WEIGHT_PARAMS,
        code='|exp'
    )

    # 合并结果
    df_result = df_query.join(ad_metrics)

    print("应用域指标计算完成")
    return df_result

def apply_ad_criteria(df_with_metrics, optimal_densLB, optimal_LdUB):
    """应用应用域判断标准"""
    print("步骤3: 应用域判断")

    print(f"相似性密度阈值 (densLB): {optimal_densLB}")
    print(f"局域不连续性阈值 (LdUB): {optimal_LdUB}")

    # 应用域判断条件
    ad_condition = (
        (df_with_metrics['simiDensity|exp'] >= optimal_densLB) &
        (df_with_metrics['simiWtLD_w|exp'] <= optimal_LdUB)
    )

    # 添加应用域判断结果
    df_result = df_with_metrics.copy()
    df_result['in_applicability_domain'] = ad_condition
    df_result['ad_densLB_threshold'] = optimal_densLB
    df_result['ad_LdUB_threshold'] = optimal_LdUB
    df_result['ad_density_value'] = df_with_metrics['simiDensity|exp']
    df_result['ad_ld_value'] = df_with_metrics['simiWtLD_w|exp']

    # 添加应用域判断原因
    def get_ad_reason(row):
        if row['in_applicability_domain']:
            return "在应用域内"
        else:
            reasons = []
            if row['ad_density_value'] < optimal_densLB:
                reasons.append(f"相似性密度({row['ad_density_value']:.3f}) < 阈值({optimal_densLB})")
            if row['ad_ld_value'] > optimal_LdUB:
                reasons.append(f"局域不连续性({row['ad_ld_value']:.3f}) > 阈值({optimal_LdUB})")
            return "; ".join(reasons)

    df_result['ad_reason'] = df_result.apply(get_ad_reason, axis=1)

    # 统计结果
    total_compounds = len(df_result)
    in_domain_count = df_result['in_applicability_domain'].sum()

    print(f"应用域判断结果:")
    print(f"总化合物数: {total_compounds}")
    print(f"应用域内: {in_domain_count} ({in_domain_count/total_compounds*100:.1f}%)")
    print(f"应用域外: {total_compounds - in_domain_count} ({(total_compounds - in_domain_count)/total_compounds*100:.1f}%)")

    return df_result

def generate_summary(df_result):
    """生成结果摘要"""
    print("\n结果摘要:")
    print("=" * 50)

    for idx, row in df_result.iterrows():
        compound_name = row.get('compound_name', f'化合物_{idx+1}')
        status = "应用域内" if row['in_applicability_domain'] else "应用域外"

        print(f"\n{compound_name}:")
        print(f"  SMILES: {row['smiles']}")
        print(f"  毒性预测: {row.get('prediction_label', 'N/A')}")
        if 'probability' in row:
            print(f"  预测概率: {row['probability']:.3f}")
        print(f"  应用域状态: {status}")
        print(f"  相似性密度: {row['ad_density_value']:.4f}")
        print(f"  局域不连续性: {row['ad_ld_value']:.4f}")
        print(f"  判断原因: {row['ad_reason']}")

        # 给出建议
        if row['in_applicability_domain']:
            suggestion = "预测结果可信，建议采用"
        else:
            suggestion = "预测结果可信度较低，建议实验验证"
        print(f"  建议: {suggestion}")

    print("=" * 50)

def run_single_prediction_with_ad():
    """运行单个SMILES预测并进行应用域判断"""
    print("=" * 60)
    print("单个SMILES预测与应用域判断")
    print("=" * 60)

    print(f"SMILES: {SINGLE_SMILES}")
    print(f"相似性密度阈值: {OPTIMAL_DENSLB}")
    print(f"局域不连续性阈值: {OPTIMAL_LDUB}")
    print()

    try:
        # 步骤1: 毒性预测
        predictor = SMILESPredictor(MODEL_PATH, use_cuda=USE_CUDA)
        prediction_result = predictor.predict_single(SINGLE_SMILES)

        if prediction_result.get('error'):
            print(f"预测失败: {prediction_result['error']}")
            return

        print("步骤1: 毒性预测完成")
        print(f"  预测结果: {prediction_result['prediction_label']}")
        print(f"  毒性概率: {prediction_result['probability']:.4f}")

        # 步骤2: 应用域判断
        df_train = load_training_data(TRAINING_DATA_PATH)

        # 创建查询数据
        query_data = {
            'smiles': [prediction_result['canonical_smiles']],
            'compound_name': [f"查询化合物_{SINGLE_SMILES[:10]}"]
        }
        df_query = pd.DataFrame(query_data)

        # 计算应用域指标
        df_with_metrics = calculate_ad_metrics(df_train, df_query)

        # 应用应用域判断标准
        df_final = apply_ad_criteria(df_with_metrics, OPTIMAL_DENSLB, OPTIMAL_LDUB)

        # 添加预测结果
        df_final['prediction'] = prediction_result['prediction']
        df_final['probability'] = prediction_result['probability']
        df_final['prediction_label'] = prediction_result['prediction_label']

        # 显示结果
        print("\n" + "=" * 60)
        print("完整分析结果:")
        print("=" * 60)

        row = df_final.iloc[0]
        status = "应用域内" if row['in_applicability_domain'] else "应用域外"

        print(f"SMILES: {SINGLE_SMILES}")
        print(f"标准SMILES: {row['smiles']}")
        print(f"毒性预测: {row['prediction_label']}")
        print(f"预测概率: {row['probability']:.4f}")
        print(f"应用域状态: {status}")
        print(f"相似性密度: {row['ad_density_value']:.4f} (阈值: {OPTIMAL_DENSLB})")
        print(f"局域不连续性: {row['ad_ld_value']:.4f} (阈值: {OPTIMAL_LDUB})")
        print(f"判断原因: {row['ad_reason']}")

        # 给出建议
        if row['in_applicability_domain']:
            suggestion = "预测结果可信，建议采用"
        else:
            suggestion = "预测结果可信度较低，建议实验验证"
        print(f"建议: {suggestion}")

        # 保存结果
        output_file = f"single_prediction_result_{SINGLE_SMILES[:10].replace('/', '_')}.xlsx"
        df_final.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")

    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("化学品毒性预测与应用域判断整合分析")
    print("=" * 50)

    print(f"预测模式: {PREDICTION_MODE}")
    if PREDICTION_MODE == 'file':
        print(f"输入文件: {INPUT_FILE}")
        print(f"输出文件: {OUTPUT_FILE}")
    else:
        print(f"单个SMILES: {SINGLE_SMILES}")
    print(f"相似性密度阈值: {OPTIMAL_DENSLB}")
    print(f"局域不连续性阈值: {OPTIMAL_LDUB}")
    print()

    try:
        if PREDICTION_MODE == 'single':
            run_single_prediction_with_ad()
        elif PREDICTION_MODE == 'file':
            # 检查输入文件
            if not os.path.exists(INPUT_FILE):
                raise FileNotFoundError(f"输入文件不存在: {INPUT_FILE}")

            # 步骤1: 运行毒性预测
            temp_prediction_file = 'temp_prediction.xlsx'
            run_prediction(INPUT_FILE, MODEL_PATH, temp_prediction_file)

            # 步骤2: 加载数据
            df_train = load_training_data(TRAINING_DATA_PATH)
            df_prediction = pd.read_excel(temp_prediction_file)

            # 步骤3: 计算应用域指标
            df_with_metrics = calculate_ad_metrics(df_train, df_prediction)

            # 步骤4: 应用应用域判断标准
            df_final = apply_ad_criteria(df_with_metrics, OPTIMAL_DENSLB, OPTIMAL_LDUB)

            # 步骤5: 保存结果
            df_final.to_excel(OUTPUT_FILE, index=False)
            print(f"\n结果已保存到: {OUTPUT_FILE}")

            # 步骤6: 生成摘要
            generate_summary(df_final)

            # 清理临时文件
            if os.path.exists(temp_prediction_file):
                os.remove(temp_prediction_file)

            print("\n分析完成！")
        else:
            raise ValueError(f"不支持的预测模式: {PREDICTION_MODE}，支持的模式: 'file', 'single'")

    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

        # 清理临时文件
        if os.path.exists('temp_prediction.xlsx'):
            os.remove('temp_prediction.xlsx')

        sys.exit(1)

if __name__ == "__main__":
    main()
