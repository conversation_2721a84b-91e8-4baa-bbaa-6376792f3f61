#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ClassyFire工具配置文件
"""

# =============================================================================
# 文件路径配置
# =============================================================================

# 输入输出文件
INPUT_FILE = 'IECSC_WangHB.xlsx'  # 输入Excel文件路径
OUTPUT_FILE = 'IECSC_WangHB_classified.xlsx'  # 输出Excel文件路径

# 列名配置
SMILES_COLUMN = 'smiles'  # SMILES列名
ID_COLUMN = None  # ID列名（可选）

# =============================================================================
# 处理参数配置
# =============================================================================

# 基本参数
START_INDEX = 0  # 开始处理的行索引（用于断点续传）
MAX_WAIT_HOURS = 48  # 最大等待时间（小时）

# API限制参数
MAX_BATCH_SIZE = 100  # 每批最大化合物数量
REQUEST_DELAY = 2.0   # 请求间隔（秒）
MAX_RETRIES = 3       # 最大重试次数
TIMEOUT = 30          # 请求超时时间（秒）

# 验证参数
MAX_SMILES_LENGTH = 2000  # SMILES最大长度
MAX_FLUORINE_COUNT = 100  # 最大氟原子数量

# =============================================================================
# 高级配置
# =============================================================================

# 日志配置
LOG_LEVEL = 'INFO'  # 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_FILE = 'classyfire.log'  # 日志文件名

# 状态文件
STATUS_FILE = 'classyfire_status.pkl'  # 状态文件
RESULTS_FILE = 'classyfire_results.pkl'  # 结果文件
REPORT_FILE = 'classyfire_report.json'  # 报告文件

# ClassyFire API配置
CLASSYFIRE_BASE_URL = "http://classyfire.wishartlab.com"
USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'

# 监控配置
CHECK_INTERVAL = 60  # 监控检查间隔（秒）
BATCH_PAUSE_INTERVAL = 30  # 每批处理后的暂停时间（秒）
BATCH_PAUSE_COUNT = 100  # 每多少个化合物暂停一次

# =============================================================================
# 过滤规则配置
# =============================================================================

# 是否启用智能过滤
ENABLE_SMART_FILTER = True

# SMILES过滤规则
SMILES_FILTERS = {
    'max_length': MAX_SMILES_LENGTH,
    'max_fluorine_count': MAX_FLUORINE_COUNT,
    'max_stereo_ratio': 0.3,  # 立体化学信息最大比例
    'forbidden_chars': ['<', '>', '|'],  # 禁用字符
}

# 化合物类型过滤（可选）
COMPOUND_TYPE_FILTERS = {
    'skip_polymers': True,  # 跳过聚合物
    'skip_mixtures': True,  # 跳过混合物
    'skip_salts': False,    # 是否跳过盐类
}

# =============================================================================
# 输出格式配置
# =============================================================================

# 输出列配置
OUTPUT_COLUMNS = [
    # 基本信息
    'kingdom_name', 'kingdom_chemont_id',
    'superclass_name', 'superclass_chemont_id', 
    'class_name', 'class_chemont_id',
    'subclass_name', 'subclass_chemont_id',
    
    # 详细分类
    'level_5_name', 'level_5_chemont_id',
    'level_6_name', 'level_6_chemont_id',
    'level_7_name', 'level_7_chemont_id',
    'level_8_name', 'level_8_chemont_id',
    
    # 父类信息
    'direct_parent_name', 'direct_parent_chemont_id',
    'alternative_parents',
    
    # 化学信息
    'canonical_smiles', 'inchikey', 'molecular_formula',
    
    # 状态信息
    'classyfire_query_id', 'classification_status'
]

# 是否包含所有原始列
INCLUDE_ORIGINAL_COLUMNS = True

# Excel格式配置
EXCEL_CONFIG = {
    'index': False,
    'engine': 'openpyxl',
    'freeze_panes': (1, 0),  # 冻结首行
}

# =============================================================================
# 错误处理配置
# =============================================================================

# 重试配置
RETRY_CONFIG = {
    'max_retries': MAX_RETRIES,
    'retry_delay': REQUEST_DELAY,
    'exponential_backoff': True,  # 指数退避
    'max_retry_delay': 60,  # 最大重试延迟
}

# 错误处理策略
ERROR_HANDLING = {
    'skip_invalid_smiles': True,  # 跳过无效SMILES
    'continue_on_error': True,    # 遇到错误继续处理
    'save_error_log': True,       # 保存错误日志
}

# =============================================================================
# 性能优化配置
# =============================================================================

# 内存管理
MEMORY_CONFIG = {
    'batch_size': MAX_BATCH_SIZE,
    'clear_cache_interval': 1000,  # 每处理多少个化合物清理缓存
    'max_memory_usage': '2GB',     # 最大内存使用量
}

# 并发配置（暂未实现）
CONCURRENCY_CONFIG = {
    'enable_concurrent': False,  # 是否启用并发
    'max_workers': 1,           # 最大工作线程数
    'concurrent_requests': 1,   # 并发请求数
}

# =============================================================================
# 调试配置
# =============================================================================

# 调试模式
DEBUG_MODE = False

# 测试配置
TEST_CONFIG = {
    'test_mode': False,      # 测试模式
    'test_sample_size': 10,  # 测试样本数量
    'dry_run': False,        # 干运行模式
}

# 详细输出
VERBOSE_CONFIG = {
    'show_progress': True,      # 显示进度
    'show_statistics': True,    # 显示统计信息
    'show_timing': True,        # 显示时间信息
    'show_memory_usage': False, # 显示内存使用
}
